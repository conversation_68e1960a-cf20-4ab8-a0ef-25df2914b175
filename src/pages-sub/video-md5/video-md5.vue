<route lang="jsonc">
{
  "style": {
    "navigationBarTitleText": "文件MD5修改",
    "navigationBarBackgroundColor": "#FFFFFF",
    "navigationBarTextStyle": "black"
  }
}
</route>

<template>
  <view class="min-h-screen bg-gray-50" :style="safeAreaStyle">
    <!-- 工具说明 -->
    <view class="p-6 mb-6 bg-white shadow-sm rounded-2xl">
      <view class="flex items-center mb-4">
        <text class="i-carbon-fingerprint-recognition text-blue-500 text-xl mr-2"></text>
        <text class="text-lg font-semibold text-gray-800">MD5修改工具</text>
      </view>
      
      <text class="text-sm text-gray-600 leading-relaxed">
        通过修改文件的MD5值，可以让相同的文件在某些平台上被识别为不同的文件。常用于规避重复检测。
      </text>
    </view>
    
    <!-- 文件选择区域 -->
    <view class="p-6 mb-6 bg-white shadow-sm rounded-2xl">
      <view class="mb-4">
        <text class="text-lg font-semibold text-gray-800">选择媒体文件</text>
        <text class="block mt-1 text-sm text-gray-500">支持相册中的图片和视频</text>
      </view>
      
      <!-- 媒体文件选择按钮 -->
      <view
        v-if="!selectedFile"
        @click="selectMedia"
        class="flex flex-col items-center justify-center py-12 border-2 border-dashed border-blue-300 rounded-xl bg-blue-50 active:bg-blue-100"
      >
        <text class="i-carbon-media text-4xl text-blue-500 mb-4"></text>
        <text class="text-blue-600 mb-2">点击选择媒体文件</text>
        <text class="text-sm text-blue-400">支持图片和视频</text>
      </view>

      <!-- 已选择的文件信息 -->
      <view v-else class="space-y-4">
        <view class="relative">
          <wd-cell-group>
            <wd-cell
              :title="selectedFile.name"
              :label="formatFileSize(selectedFile.size)"
            >
              <template #icon>
                <view class="w-12 h-12 rounded-lg overflow-hidden mr-3 bg-gray-100 flex items-center justify-center">
                  <!-- 图片预览 -->
                  <image
                    v-if="selectedFile.type === 'image'"
                    :src="selectedFile.path"
                    class="w-full h-full object-cover"
                    mode="aspectFill"
                  />
                  <!-- 视频预览（显示视频图标） -->
                  <view v-else class="w-full h-full bg-gray-800 flex items-center justify-center">
                    <wd-icon name="video" color="white" size="24px"></wd-icon>
                  </view>
                </view>
              </template>
            </wd-cell>
          </wd-cell-group>

          <!-- 删除按钮 -->
          <view class="absolute top-1/2 right-4 transform -translate-y-1/2">
            <wd-button
              type="error"
              size="small"
              round
              @click="removeFile"
              custom-style="width: 28px; height: 28px; min-width: 28px; padding: 0;"
            >
              <wd-icon name="close" size="12px"></wd-icon>
            </wd-button>
          </view>
        </view>

        <!-- MD5信息 -->
        <wd-cell-group v-if="originalMD5 || modifiedFile?.md5" title="MD5信息">
          <wd-cell
            v-if="originalMD5"
            title="原始MD5值"
            :value="originalMD5"
            icon="fingerprint"
          >
            <template #value>
              <text class="text-xs font-mono text-gray-600 break-all">{{ originalMD5 }}</text>
            </template>
          </wd-cell>

          <wd-cell
            v-if="modifiedFile?.md5"
            title="新MD5值"
            :value="modifiedFile.md5"
            icon="check-circle"
          >
            <template #value>
              <text class="text-xs font-mono text-green-600 break-all">{{ modifiedFile.md5 }}</text>
            </template>
          </wd-cell>
        </wd-cell-group>
      </view>
    </view>
    

    
    <!-- 操作按钮 -->
    <view v-if="selectedFile" class="p-6 mb-6 bg-white shadow-sm rounded-2xl">
      <wd-button
        type="primary"
        size="large"
        block
        round
        :loading="isProcessing"
        :disabled="isProcessing"
        @click="startModify"
      >
        <wd-icon name="refresh" v-if="!isProcessing"></wd-icon>
        {{ isProcessing ? '处理中...' : '开始修改MD5' }}
      </wd-button>

      <view v-if="processProgress > 0" class="mt-4">
        <wd-progress
          :percentage="processProgress"
          :show-text="true"
          color="#4f46e5"
        ></wd-progress>
      </view>
    </view>
    
    <!-- 处理结果 -->
    <wd-message-box
      v-if="modifiedFile"
      type="success"
      title="处理完成"
      message="文件MD5已成功修改并保存到相册"
      :show-close="false"
    ></wd-message-box>
    
    <!-- 注意事项 -->
    <view class="p-6 mb-20 bg-white shadow-sm rounded-2xl">
      <view class="flex items-center mb-4">
        <text class="i-carbon-warning text-orange-500 text-xl mr-2"></text>
        <text class="text-lg font-semibold text-gray-800">注意事项</text>
      </view>
      
      <view class="space-y-2 text-sm text-gray-600">
        <text class="block">• 修改MD5不会影响文件的实际内容和质量</text>
        <text class="block">• 处理大文件可能需要较长时间，请耐心等待</text>
        <text class="block">• 建议在WiFi环境下使用，避免消耗过多流量</text>
        <text class="block">• 请合理使用此功能，遵守相关平台规则</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { getSafeAreaStyle } from '@/utils/safeArea'

// 获取安全区域样式
const safeAreaStyle = getSafeAreaStyle()

// 响应式数据
const selectedFile = ref<any>(null)
const originalMD5 = ref('')
const isProcessing = ref(false)
const processProgress = ref(0)
const modifiedFile = ref<any>(null)
const fileSystemManager = uni.getFileSystemManager()

// 下载网络文件到本地
const downloadToLocal = (url: string, fileExtension: string): Promise<any> => {
  return new Promise((resolve, reject) => {
    uni.downloadFile({
      url: url,
      success: (res) => {
        if (res.statusCode === 200) {
          console.log('文件下载成功:', res.tempFilePath)
          resolve({ tempFilePath: res.tempFilePath })
        } else {
          reject(new Error(`下载失败，状态码: ${res.statusCode}`))
        }
      },
      fail: (error) => {
        console.error('下载文件失败:', error)
        reject(error)
      }
    })
  })
}



// 选择媒体文件（图片或视频）
const selectMedia = () => {
  uni.chooseMedia({
    count: 1,
    mediaType: ['image', 'video'],
    sourceType: ['album', 'camera'],
    maxDuration: 30,
    camera: 'back',
    success: async (res) => {
      if (res.tempFiles && res.tempFiles.length > 0) {
        const file = res.tempFiles[0]
        const fileType = file.fileType || 'image'
        const fileExtension = fileType === 'video' ? '.mp4' : '.jpg'

        console.log('选择的文件信息:', file)
        console.log('文件路径:', file.tempFilePath)
        console.log('文件大小:', file.size)

        // 如果路径是http开头，需要先下载到本地
        let localPath = file.tempFilePath
        if (file.tempFilePath.startsWith('http')) {
          try {
            console.log('检测到网络路径，开始下载到本地...')
            const downloadResult = await downloadToLocal(file.tempFilePath, fileExtension)
            localPath = downloadResult.tempFilePath
            console.log('下载到本地路径:', localPath)
          } catch (error) {
            console.error('下载文件到本地失败:', error)
            uni.showToast({
              title: '文件处理失败',
              icon: 'none'
            })
            return
          }
        }

        selectedFile.value = {
          name: `${fileType}_${Date.now()}${fileExtension}`,
          size: file.size || 0,
          path: localPath,
          type: fileType
        }

        // 计算原始MD5
        calculateOriginalMD5()
      }
    },
    fail: (err) => {
      console.error('选择媒体文件失败:', err)
      uni.showToast({
        title: '选择媒体文件失败',
        icon: 'none'
      })
    }
  })
}

// 移除文件
const removeFile = () => {
  selectedFile.value = null
  originalMD5.value = ''
  modifiedFile.value = null
  processProgress.value = 0
}



// 获取文件MD5值
const getFileMD5 = (filePath: string): Promise<string> => {
  return new Promise((resolve, reject) => {
    uni.getFileInfo({
      filePath,
      digestAlgorithm: 'md5',
      success: (res) => {
        console.log('文件MD5获取成功:', res.digest)
        resolve(res.digest || '')
      },
      fail: (error) => {
        console.error('获取文件MD5失败:', error)
        reject(error)
      }
    })
  })
}

// 计算文件MD5值
const calculateOriginalMD5 = async () => {
  if (!selectedFile.value?.path) return

  try {
    const md5 = await getFileMD5(selectedFile.value.path)
    originalMD5.value = md5
    if (!md5) {
      uni.showToast({
        title: '无法获取文件MD5',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('计算MD5失败:', error)
    uni.showToast({
      title: '计算MD5失败',
      icon: 'none'
    })
  }
}

// 开始修改MD5
const startModify = async () => {
  if (!selectedFile.value) return

  isProcessing.value = true
  processProgress.value = 0

  try {
    // 直接复制文件并添加随机数据
    processProgress.value = 20
    const newFilePath = await copyAndModifyFile(selectedFile.value.path)

    // 计算新文件的MD5值
    processProgress.value = 80
    const newMD5 = await getFileMD5(newFilePath)

    // 获取新文件大小
    processProgress.value = 90
    const fileInfo = await getFileInfo(newFilePath)

    // 完成处理
    processProgress.value = 100
    completeModification(newMD5, newFilePath, fileInfo.size)

  } catch (error) {
    console.error('修改MD5失败:', error)
    uni.showToast({
      title: '修改失败',
      icon: 'none'
    })
    isProcessing.value = false
    processProgress.value = 0
  }
}

// 复制文件并添加随机数据来修改MD5
const copyAndModifyFile = (originalPath: string): Promise<string> => {
  return new Promise((resolve, reject) => {
    const fileName = selectedFile.value.name
    const fileExtension = fileName.substring(fileName.lastIndexOf('.'))
    const newFileName = fileName.replace(fileExtension, `_modified${fileExtension}`)

    // 获取新文件路径
    let newPath: string
    // #ifdef MP-WEIXIN
    newPath = `${wx.env.USER_DATA_PATH}/${newFileName}`
    // #endif
    // #ifndef MP-WEIXIN
    newPath = `/tmp/${newFileName}`
    // #endif

    console.log('开始复制文件:', originalPath, '->', newPath)

    // 使用copyFile复制文件
    fileSystemManager.copyFile({
      srcPath: originalPath,
      destPath: newPath,
      success: () => {
        console.log('文件复制成功')

        // 在文件末尾添加随机数据
        const randomData = `MD5_MODIFIER_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`

        fileSystemManager.appendFile({
          filePath: newPath,
          data: randomData,
          encoding: 'utf8',
          success: () => {
            console.log('随机数据添加成功')
            resolve(newPath)
          },
          fail: (error) => {
            console.error('添加随机数据失败:', error)
            reject(error)
          }
        })
      },
      fail: (error) => {
        console.error('文件复制失败:', error)
        reject(error)
      }
    })
  })
}

// 获取文件信息
const getFileInfo = (filePath: string): Promise<any> => {
  return new Promise((resolve, reject) => {
    fileSystemManager.getFileInfo({
      filePath,
      success: (res) => {
        resolve(res)
      },
      fail: (error) => {
        reject(error)
      }
    })
  })
}



// 保存到相册
const saveToAlbum = (filePath: string, fileType: string): Promise<void> => {
  return new Promise((resolve, reject) => {
    if (fileType === 'image') {
      // 保存图片到相册
      uni.saveImageToPhotosAlbum({
        filePath,
        success: () => {
          console.log('图片保存到相册成功')
          resolve()
        },
        fail: (error) => {
          console.error('图片保存到相册失败:', error)
          reject(error)
        }
      })
    } else if (fileType === 'video') {
      // 保存视频到相册
      uni.saveVideoToPhotosAlbum({
        filePath,
        success: () => {
          console.log('视频保存到相册成功')
          resolve()
        },
        fail: (error) => {
          console.error('视频保存到相册失败:', error)
          reject(error)
        }
      })
    } else {
      reject(new Error('不支持的文件类型'))
    }
  })
}

// 完成修改
const completeModification = async (newMD5: string, newFilePath: string, newSize: number) => {
  isProcessing.value = false

  modifiedFile.value = {
    name: selectedFile.value.name.replace(/(\.[^.]+)$/, '_modified$1'),
    size: newSize,
    md5: newMD5,
    path: newFilePath
  }

  // 自动保存到相册
  try {
    await saveToAlbum(newFilePath, selectedFile.value.type)

    uni.showToast({
      title: '修改完成并已保存到相册',
      icon: 'success',
      duration: 2000
    })
  } catch (error) {
    console.error('保存到相册失败:', error)
    uni.showToast({
      title: '修改完成，但保存到相册失败',
      icon: 'none',
      duration: 2000
    })
  }
}



// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}
</script>

<style scoped>
.space-y-4 > view:not(:first-child) {
  margin-top: 1rem;
}

.space-y-3 > view:not(:first-child) {
  margin-top: 0.75rem;
}

.space-y-2 > text:not(:first-child) {
  margin-top: 0.5rem;
}

.break-all {
  word-break: break-all;
}
</style>
